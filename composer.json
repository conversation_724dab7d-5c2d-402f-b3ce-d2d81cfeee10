{"name": "startupium/backend", "type": "project", "description": "Laravel startupium project.", "keywords": ["startupium", "laravel", "backend"], "license": "MIT", "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://startupium.ru"}], "require": {"php": "^8.2", "darkaonline/l5-swagger": "^8.6", "doctrine/dbal": "^3.8", "filament/filament": "^3.3", "guzzlehttp/guzzle": "^7.8", "laravel/framework": "^11.0", "laravel/passport": "^12.0", "laravel/sanctum": "^4.0", "laravel/socialite": "^5.14", "laravel/telescope": "^5.0", "laravel/tinker": "^2.9", "league/flysystem-aws-s3-v3": "^3.27", "monolog/monolog": "^3.6", "predis/predis": "^2.1", "spatie/laravel-medialibrary": "^11.8"}, "require-dev": {"fakerphp/faker": "^1.23", "friendsofphp/php-cs-fixer": "^3.56", "laravel/pint": "^1.15", "laravel/sail": "^1.29", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.1", "pestphp/pest": "^2.34", "pestphp/pest-plugin-laravel": "^2.4", "spatie/laravel-ignition": "^2.7"}, "autoload": {"psr-4": {"App\\": "app/", "Domain\\": "src/Domain", "Services\\": "src/Services", "Support\\": "src/Support", "Routing\\": "src/Routing", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"files": ["app/helpers.php", "src/Support/helpers.php", "src/Services/Crypto/helper.php", "app/SwaggerDocs/helpers.php"], "classmap": ["tests", "database/seeds", "database/factories"], "psr-4": {"App\\": "app/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi", "@php artisan filament:upgrade"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}