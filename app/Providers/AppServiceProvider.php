<?php
namespace App\Providers;

use Carbon\CarbonInterval;
use Illuminate\Database\Connection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        //Password::enablePasswordGrant();
        $this->app['request']->server->set('HTTPS',config('app.https_is_on'));
        if (App::environment('production')) {
            URL::forceScheme('https');}
    }

    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        // При локальной разработке.
        // Сработает при N+1 проблеме запросов и выдаст ошибку
        Model::preventLazyLoading(!app()->isProduction());

        // Если сохраняем поле, которого нет в свойстве fillable, то будет ошибка
        //Model::preventSilentlyDiscardingAttributes(!app()->isProduction());

        // Выполнится если запрос к БД длится долго
        DB::whenQueryingForLongerThan(500, function (Connection $connection) {
            logger()
                ->channel("telegram")
                ->debug("whenQueryingForLongerThan:" . $connection?->query()?->toSql());
        });


        // В Laravel 11 функциональность whenRequestLifecycleIsLongerThan была удалена
        // Можно использовать middleware или другие способы мониторинга производительности
    }
}
