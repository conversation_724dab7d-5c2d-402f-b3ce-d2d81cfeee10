<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

// Open API routes (без аутентификации)
Route::group([], function () {
    require base_path('routes/api/open/seo.php');
    require base_path('routes/api/open/blog.php');
    require base_path('routes/api/open/combine.php');
    require base_path('routes/api/open/auth.php');
    require base_path('routes/api/open/user.php');
    require base_path('routes/api/open/project.php');
    require base_path('routes/api/open/comment.php');
    require base_path('routes/api/open/residence.php');
    require base_path('routes/api/open/subscribers.php');
    require base_path('routes/api/open/feedback.php');
});

// Protected API routes (с аутентификацией)
Route::middleware(['auth:api', 'token'])->group(function () {
    require base_path('routes/api/protected/blog.php');
    require base_path('routes/api/protected/wiki.php');
    require base_path('routes/api/protected/auth.php');
    require base_path('routes/api/protected/chat.php');
    require base_path('routes/api/protected/comment.php');
    require base_path('routes/api/protected/profile.php');
    require base_path('routes/api/protected/project.php');
    require base_path('routes/api/protected/tracker.php');
    require base_path('routes/api/protected/notification.php');
    require base_path('routes/api/protected/centrifugo.php');
    require base_path('routes/api/protected/subscribers.php');
    require base_path('routes/api/protected/communication.php');
});

// Admin API routes (с аутентификацией и правами админа)
Route::middleware(['auth:api', 'token', 'admin'])->group(function () {
    require base_path('routes/api/admin/profile.php');
});
