<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('tracker_tasks', function (Blueprint $table) {
            $table->integer('hours_estimation')->default(0)->after('column_id');
            $table->integer('minutes_estimation')->default(0)->after('column_id');
        });
    }

    public function down(): void
    {
        Schema::table('tracker_tasks', function (Blueprint $table) {
            $table->dropColumn('hours_estimation');
            $table->dropColumn('minutes_estimation');
        });
    }
};
