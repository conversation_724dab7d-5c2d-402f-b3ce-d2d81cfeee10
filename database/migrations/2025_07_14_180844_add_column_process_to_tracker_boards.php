<?php

use Domain\Tracker\Enums\BoardInProgressEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('tracker_boards', function (Blueprint $table) {
            $table->string('progress')->default(BoardInProgressEnum::AT_WORK)->after('title');
        });
    }

    public function down(): void
    {
        Schema::table('tracker_boards', function (Blueprint $table) {
            $table->dropColumn('progress');
        });
    }
};
