# Git
pull-job:
  stage: .pre
  tags:
    - backend
  only:
    - main
  script:
    - sudo sh -c "cd /data/repository/backend; git pull https://startupium:<EMAIL>/startupium/backend.git"

# Artisan
build-job:
  stage: build
  tags:
    - backend
  only:
    - main
  script:
    - sudo sh -c "cd /data;  docker-compose exec -it backend composer install; docker-compose exec -it backend php artisan cache:clear; docker-compose exec -it backend php artisan config:clear; docker-compose exec -it backend php artisan config:cache; docker-compose exec -it backend php artisan migrate; docker-compose exec -it backend php artisan route:clear;"

# Clear
clear-job:
  stage: .post
  tags:
    - backend
  only:
    - main
  script:
    - sudo sh -c "docker system prune -a -f;"