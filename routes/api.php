<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

// Include existing API routes
foreach (glob(__DIR__ . '/api/*.php') as $file) {
    require $file;
}

// Include API subdirectories
foreach (glob(__DIR__ . '/api/*/*.php') as $file) {
    require $file;
}
