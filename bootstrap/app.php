<?php

use App\Http\Middleware\DecryptMiddleware;
use App\Http\Middleware\IsUserOnline;
use App\Http\Middleware\LastOnlineAt;
use Illuminate\Cache\RateLimiting\Limit;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use Illuminate\Http\Request;
use Illuminate\Http\Response as HttpResponse;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Facades\Route;

return Application::configure(basePath: dirname(__DIR__))
    ->withProviders()
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        api: __DIR__.'/../routes/api.php',
        commands: __DIR__.'/../routes/console.php',
        channels: __DIR__.'/../routes/channels.php',
        health: '/up',
        then: function () {
            // Дополнительные маршруты из RouteServiceProvider
            Route::middleware('web')
                ->group(function() {
                    require base_path('routes/web/swagger.php');
                });
        }
    )
    ->withBroadcasting(
        channels: __DIR__.'/../routes/channels.php',
        attributes: [
            'prefix' => 'api',
            'middleware' => ['auth:api'],
        ],
    )
    ->withCommands([
        __DIR__.'/../app/Console/Commands',
    ])
    ->withMiddleware(function (Middleware $middleware) {
        // Global middleware
        $middleware->append([
            \Illuminate\Foundation\Http\Middleware\ValidatePostSize::class,
            \App\Http\Middleware\TrimStrings::class,
            \Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull::class,
            \App\Http\Middleware\TrustProxies::class,
            \Illuminate\Http\Middleware\HandleCors::class,
            DecryptMiddleware::class,
        ]);

        // Web middleware group
        $middleware->appendToGroup('web', [
            \App\Http\Middleware\EncryptCookies::class,
            \Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse::class,
            \Illuminate\Session\Middleware\StartSession::class,
            \Illuminate\Session\Middleware\AuthenticateSession::class,
            \Illuminate\View\Middleware\ShareErrorsFromSession::class,
            \App\Http\Middleware\VerifyCsrfToken::class,
            \Illuminate\Routing\Middleware\SubstituteBindings::class,
            LastOnlineAt::class,
            IsUserOnline::class,
        ]);

        // API middleware group
        $middleware->appendToGroup('api', [
            'throttle:global',
            \Illuminate\Routing\Middleware\SubstituteBindings::class,
            LastOnlineAt::class,
            IsUserOnline::class,
        ]);

        // Route middleware aliases
        $middleware->alias([
            'auth' => \App\Http\Middleware\Authenticate::class,
            'auth.basic' => \Illuminate\Auth\Middleware\AuthenticateWithBasicAuth::class,
            'bindings' => \Illuminate\Routing\Middleware\SubstituteBindings::class,
            'cache.headers' => \Illuminate\Http\Middleware\SetCacheHeaders::class,
            'can' => \Illuminate\Auth\Middleware\Authorize::class,
            'guest' => \App\Http\Middleware\RedirectIfAuthenticated::class,
            'signed' => \Illuminate\Routing\Middleware\ValidateSignature::class,
            'throttle' => \Illuminate\Routing\Middleware\ThrottleRequests::class,
            'verified' => \Illuminate\Auth\Middleware\EnsureEmailIsVerified::class,
            'token' => \App\Http\Middleware\ValidateRefreshToken::class,
            'auth.pagination' => \App\Http\Middleware\AuthPagination::class,
            'admin' => \App\Http\Middleware\CheckAdminRoot::class,
            'reCaptcha' => \App\Http\Middleware\ReCaptcha::class,
            'encrypt' => \App\Http\Middleware\EncryptMiddleware::class,
            'tracker.bindings' => \App\Http\Middleware\TaskTrackerAutoBinding::class,
        ]);

        // Middleware priority
        $middleware->priority([
            \Illuminate\Session\Middleware\StartSession::class,
            \Illuminate\View\Middleware\ShareErrorsFromSession::class,
            \App\Http\Middleware\Authenticate::class,
            \Illuminate\Session\Middleware\AuthenticateSession::class,
            \Illuminate\Routing\Middleware\SubstituteBindings::class,
            \Illuminate\Auth\Middleware\Authorize::class,
        ]);

        // Rate limiting
        $middleware->throttleApi('global');
    })
    ->withExceptions(function (Exceptions $exceptions) {
        //
    })
    ->booting(function () {
        // Rate limiting configuration
        RateLimiter::for('global', function(Request $request) {
            return Limit::perMinute(300)
                ->by($request->ip())
                ->response(function (Request $request, array $headers) {
                    return response()->json([
                            'message' => config('constants.too_many_requests'),
                        ], HttpResponse::HTTP_TOO_MANY_REQUESTS, $headers
                    );
            });
        });

        RateLimiter::for('login', function(Request $request) {
            return Limit::perMinute(10)
                ->by($request->ip())
                ->response(function (Request $request, array $headers) {
                    return response()->json([
                            'message' => config('constants.too_many_login_attempts'),
                        ], HttpResponse::HTTP_TOO_MANY_REQUESTS, $headers
                    );
            });
        });
    })
    ->create();
