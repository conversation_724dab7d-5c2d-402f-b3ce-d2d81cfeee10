<?php return array (
  'providers' => 
  array (
    0 => 'BladeUI\\Heroicons\\BladeHeroiconsServiceProvider',
    1 => 'BladeUI\\Icons\\BladeIconsServiceProvider',
    2 => 'L5Swagger\\L5SwaggerServiceProvider',
    3 => 'Filament\\Actions\\ActionsServiceProvider',
    4 => 'Filament\\FilamentServiceProvider',
    5 => 'Filament\\Forms\\FormsServiceProvider',
    6 => 'Filament\\Infolists\\InfolistsServiceProvider',
    7 => 'Filament\\Notifications\\NotificationsServiceProvider',
    8 => 'Filament\\Support\\SupportServiceProvider',
    9 => 'Filament\\Tables\\TablesServiceProvider',
    10 => 'Filament\\Widgets\\WidgetsServiceProvider',
    11 => 'Ki<PERSON>baum\\PowerJoins\\PowerJoinsServiceProvider',
    12 => 'Laravel\\Passport\\PassportServiceProvider',
    13 => '<PERSON><PERSON>\\Sail\\SailServiceProvider',
    14 => '<PERSON>vel\\Sanctum\\SanctumServiceProvider',
    15 => 'Laravel\\Socialite\\SocialiteServiceProvider',
    16 => 'Laravel\\Telescope\\TelescopeServiceProvider',
    17 => 'Laravel\\Tinker\\TinkerServiceProvider',
    18 => 'Livewire\\LivewireServiceProvider',
    19 => 'Carbon\\Laravel\\ServiceProvider',
    20 => 'NunoMaduro\\Collision\\Adapters\\Laravel\\CollisionServiceProvider',
    21 => 'Termwind\\Laravel\\TermwindServiceProvider',
    22 => 'Pest\\Laravel\\PestServiceProvider',
    23 => 'RyanChandler\\BladeCaptureDirective\\BladeCaptureDirectiveServiceProvider',
    24 => 'Spatie\\LaravelIgnition\\IgnitionServiceProvider',
    25 => 'Spatie\\MediaLibrary\\MediaLibraryServiceProvider',
    26 => 'Laravel\\Socialite\\SocialiteServiceProvider',
    27 => 'App\\Providers\\AppServiceProvider',
    28 => 'App\\Providers\\AuthServiceProvider',
    29 => 'App\\Providers\\EventServiceProvider',
    30 => 'App\\Providers\\Filament\\AdminPanelProvider',
    31 => 'App\\Providers\\TelescopeServiceProvider',
    32 => 'App\\Providers\\CentrifugoServiceProvider',
    33 => 'App\\Providers\\DomainServiceProvider',
    34 => 'Services\\AI\\BotHubServiceProvider',
    35 => 'L5Swagger\\L5SwaggerServiceProvider',
    36 => 'App\\Providers\\AppServiceProvider',
    37 => 'App\\Providers\\AuthServiceProvider',
    38 => 'App\\Providers\\CentrifugoServiceProvider',
    39 => 'App\\Providers\\DomainServiceProvider',
    40 => 'App\\Providers\\EventServiceProvider',
    41 => 'App\\Providers\\Filament\\AdminPanelProvider',
    42 => 'App\\Providers\\TelescopeServiceProvider',
  ),
  'eager' => 
  array (
    0 => 'BladeUI\\Heroicons\\BladeHeroiconsServiceProvider',
    1 => 'BladeUI\\Icons\\BladeIconsServiceProvider',
    2 => 'L5Swagger\\L5SwaggerServiceProvider',
    3 => 'Filament\\Actions\\ActionsServiceProvider',
    4 => 'Filament\\FilamentServiceProvider',
    5 => 'Filament\\Forms\\FormsServiceProvider',
    6 => 'Filament\\Infolists\\InfolistsServiceProvider',
    7 => 'Filament\\Notifications\\NotificationsServiceProvider',
    8 => 'Filament\\Support\\SupportServiceProvider',
    9 => 'Filament\\Tables\\TablesServiceProvider',
    10 => 'Filament\\Widgets\\WidgetsServiceProvider',
    11 => 'Kirschbaum\\PowerJoins\\PowerJoinsServiceProvider',
    12 => 'Laravel\\Passport\\PassportServiceProvider',
    13 => 'Laravel\\Sanctum\\SanctumServiceProvider',
    14 => 'Laravel\\Telescope\\TelescopeServiceProvider',
    15 => 'Livewire\\LivewireServiceProvider',
    16 => 'Carbon\\Laravel\\ServiceProvider',
    17 => 'NunoMaduro\\Collision\\Adapters\\Laravel\\CollisionServiceProvider',
    18 => 'Termwind\\Laravel\\TermwindServiceProvider',
    19 => 'Pest\\Laravel\\PestServiceProvider',
    20 => 'RyanChandler\\BladeCaptureDirective\\BladeCaptureDirectiveServiceProvider',
    21 => 'Spatie\\LaravelIgnition\\IgnitionServiceProvider',
    22 => 'Spatie\\MediaLibrary\\MediaLibraryServiceProvider',
    23 => 'App\\Providers\\AppServiceProvider',
    24 => 'App\\Providers\\AuthServiceProvider',
    25 => 'App\\Providers\\EventServiceProvider',
    26 => 'App\\Providers\\Filament\\AdminPanelProvider',
    27 => 'App\\Providers\\TelescopeServiceProvider',
    28 => 'App\\Providers\\CentrifugoServiceProvider',
    29 => 'App\\Providers\\DomainServiceProvider',
    30 => 'Services\\AI\\BotHubServiceProvider',
    31 => 'L5Swagger\\L5SwaggerServiceProvider',
    32 => 'App\\Providers\\AppServiceProvider',
    33 => 'App\\Providers\\AuthServiceProvider',
    34 => 'App\\Providers\\CentrifugoServiceProvider',
    35 => 'App\\Providers\\DomainServiceProvider',
    36 => 'App\\Providers\\EventServiceProvider',
    37 => 'App\\Providers\\Filament\\AdminPanelProvider',
    38 => 'App\\Providers\\TelescopeServiceProvider',
  ),
  'deferred' => 
  array (
    'Laravel\\Sail\\Console\\InstallCommand' => 'Laravel\\Sail\\SailServiceProvider',
    'Laravel\\Sail\\Console\\PublishCommand' => 'Laravel\\Sail\\SailServiceProvider',
    'Laravel\\Socialite\\Contracts\\Factory' => 'Laravel\\Socialite\\SocialiteServiceProvider',
    'command.tinker' => 'Laravel\\Tinker\\TinkerServiceProvider',
  ),
  'when' => 
  array (
    'Laravel\\Sail\\SailServiceProvider' => 
    array (
    ),
    'Laravel\\Socialite\\SocialiteServiceProvider' => 
    array (
    ),
    'Laravel\\Tinker\\TinkerServiceProvider' => 
    array (
    ),
  ),
);