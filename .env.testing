APP_NAME=Laravel
APP_ENV=local
APP_KEY=base64:9FT640If0llgcyGMCyyig42wvbRjS5bho89QCjgka8k=
APP_DEBUG=true

# используется библиотекой spatie/laravel-medialibrary (картинки, аватарки) и laravel
APP_URL=http://localhost:8080

# для работы CORS надо устновить URL сервера с которого будут выполняться запросы
# * - разрешено со всех доменов
# например 'http://localhost:3000'
# также используется для создания ссылки для сброса пароля в config
APP_ACCESS_CONTROL_ALLOW_ORIGIN=http://localhost:3000

LOG_CHANNEL=stack
LOG_LEVEL=debug

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=33062
DB_DATABASE=startupium_test
DB_USERNAME=admin_test
DB_PASSWORD=secret_test

BROADCAST_DRIVER=redis
CACHE_DRIVER=redis
QUEUE_CONNECTION=database
SESSION_DRIVER=file
SESSION_LIFETIME=48000
SESSION_DOMAIN=localhost

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_DRIVER=smtp
MAIL_HOST=localhost
MAIL_PORT=1025
MAIL_USERNAME=mailhog
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_APP_CLUSTER=mt1

MIX_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
MIX_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

GOOGLE_CLIENT_ID=586946055858-bcjv014vr5qpgm5r59le5kr75odmq0l2.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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
GOOGLE_REDIRECT=http://localhost:3000/auth/google

# Telegram bot settings
TELEGRAM_BOT_TOKEN=**********************************************
TELEGRAM_CHAT_ID=-933894179

# https settings ( on/off )
APP_HTTPS_IS_ON=off

# centrifugo
CENTRIFUGO_SECRET=secret
CENTRIFUGO_APIKEY=startupium_app_key
CENTRIFUGO_URL=http://*************:8086
CENTRIFUGO_SSL_KEY=
CENTRIFUGO_VERIFY=false

# AWS
AWS_ACCESS_KEY_ID=cactus9939
AWS_SECRET_ACCESS_KEY=ca46add809b9a5cf8660ff4642056bb7
AWS_DEFAULT_REGION=ru-1
AWS_BUCKET=98d25733-0c17313a-1b70-4cf2-b883-e64c42748d66
AWS_URL=https://s3.timeweb.com
AWS_ENDPOINT=https://s3.timeweb.com

FILESYSTEM_CLOUD=minio
MEDIA_DISK=minio

# crypto
CIPHER_SECRET_KEY=e7S3wYsTYTdrDpjp4924mDGGgrDw
# with_cipher or default enter true|false
WITH_CIPHER=false

TELESCOPE_ENABLED=false

# Google siteverify secret key
RECAPTCHA_SECRET_KEY=6LfUpaInAAAAAN8PJw3AdvO46VKVIhi8yhtvSpAy
# send feedback and signup useing recaptcha or not true|false
WITH_RECAPTCHA=false

