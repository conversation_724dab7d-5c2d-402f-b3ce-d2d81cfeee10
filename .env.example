APP_NAME=Laravel
APP_ENV=local
APP_KEY=
APP_DEBUG=true

# используется библиотекой spatie/laravel-medialibrary (картинки, аватарки) и laravel
APP_URL=http://localhost:8080

# для работы CORS надо устновить URL сервера с которого будут выполняться запросы
# * - разрешено со всех доменов
# например 'http://localhost:3000'
# также используется для создания ссылки для сброса пароля в config
APP_ACCESS_CONTROL_ALLOW_ORIGIN='http://localhost:3000'

# swagger host
L5_SWAGGER_CONST_HOST=http://dev.startupium.ru:8000/api
L5_SWAGGER_CONST_TYPE=http

LOG_CHANNEL=stack
LOG_LEVEL=debug

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=laravel
DB_USERNAME=root
DB_PASSWORD=

BROADCAST_DRIVER=log
CACHE_DRIVER=file
FILESYSTEM_DRIVER=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120
SESSION_DOMAIN="${APP_URL}"
# Для установки cookie laravel_session и XSRF-TOKEN при https, установить в true
SESSION_SECURE_COOKIE=true

MEMCACHED_HOST=127.0.0.1

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_HOST=mailhog
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS=null
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_APP_CLUSTER=mt1

MIX_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
MIX_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

# Telegram bot settings
TELEGRAM_BOT_TOKEN=token
TELEGRAM_CHAT_ID=is

# https settings ( on/off )
APP_HTTPS_IS_ON=off

# crypto ("Startupium" by default - config->crypto)
CIPHER_SECRET_KEY=

# Google siteverify secret key
RECAPTCHA_V3_FEEDBACK_SECRET_KEY=key
RECAPTCHA_V3_SIGNUP_SECRET_KEY=key

# send feedback and signup useing recaptcha or not true|false
WITH_RECAPTCHA="true | false"
# with_cipher or default enter true|false
WITH_CIPHER="true | false"

# Artificial intelligence now BotHub
AI_API_KEY="ai_api_key"
AI_BASE_URL="ai_base_url"
AI_MODEL="ai_model"

# VIP проекты (массив ID), например: VIP_PROJECTS_ID=1,5,10
VIP_PROJECTS_ID=

